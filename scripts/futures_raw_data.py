segment = "stock"

universe = "raw_futstk_fut"

ipath = f"/mnt/hft_data/one_minute_trd/parquet/{segment}_futures"

symbol_id_to_symbol = {
    "5001": "NIFTY",
    "5002": "BANKNIFTY",
    "5003": "FINNIFTY",
    "5008": "MIDCPNIFTY",
}


import datetime
import numpy as np
import pandas as pd
import zipfile
import os, time
from multiprocessing import Pool


def futures_raw(symbol_id):
    print(f"Started for {symbol_id}")

    symbol = symbol_id_to_symbol[symbol_id]

    for expiry in os.listdir(ipath):
        path = f"{ipath}/{expiry}"

        data = pd.read_parquet(path)

        data = data.reset_index()

        data = data[data["symbol"] == symbol]

        data["timestamp"] = pd.to_datetime(
            data["date"].astype(str) + " " + data["time"].astype(str)
        )

        data = data.set_index("timestamp")

        data = data.rename(
            columns={
                "ltp_open": "Open",
                "ltp_high": "High",
                "ltp_low": "Low",
                "ltp_close": "Close",
                "ltv_total": "Volume",
            }
        )

        data["ID"] = expiry[4:8] + expiry[2:4] + expiry[:2] + "2" + symbol_id

        data["ID"] = data["ID"].astype(dtype="uint64")

        data = data[["ID", "Open", "High", "Low", "Close", "Volume"]]

        data["Open"] = data["Open"] / 100
        data["High"] = data["High"] / 100
        data["Low"] = data["Low"] / 100
        data["Close"] = data["Close"] / 100

        if len(data) > 0:
            data.to_parquet(
                f"/home/<USER>/repos/data_auditing/{universe}/{symbol_id}/expiry_wise_parquets/{expiry}.parquet"
            )

        print(f"Completed for {symbol_id}")


# def futures_raw_multi():
#     symbol_list = ["5008", "5003", "5002", "5001"]

#     with Pool() as p:
#         p.map(futures_raw, symbol_list)


# futures_raw_multi()


def futures_stocks_raw():
    print(f"Started")

    for expiry in os.listdir(ipath):
        path = f"{ipath}/{expiry}"

        data = pd.read_parquet(path)

        data = data.reset_index()

        data["timestamp"] = pd.to_datetime(
            data["date"].astype(str) + " " + data["time"].astype(str)
        )

        data = data.set_index("timestamp")

        data = data.rename(
            columns={
                "ltp_open": "Open",
                "ltp_high": "High",
                "ltp_low": "Low",
                "ltp_close": "Close",
                "ltv_total": "Volume",
            }
        )

        data["Open"] = data["Open"] / 100

        data["High"] = data["High"] / 100

        data["Low"] = data["Low"] / 100

        data["Close"] = data["Close"] / 100

        for symbol in data["symbol"].unique():
            datas = data[data["symbol"] == symbol]

            datas["ID"] = symbol + datetime.datetime.strptime(
                expiry[:8], "%d%m%Y"
            ).strftime("%d-%b-%Y")

            datas = datas[["ID", "Open", "High", "Low", "Close", "Volume"]]

            if len(datas) > 0:
                if not os.path.isdir(
                    f"/home/<USER>/repos/data_auditing/{universe}/{symbol}/expiry_wise_parquets"
                ):
                    os.makedirs(
                        f"/home/<USER>/repos/data_auditing/{universe}/{symbol}/expiry_wise_parquets"
                    )

                datas.to_parquet(
                    f"/home/<USER>/repos/data_auditing/{universe}/{symbol}/expiry_wise_parquets/{expiry}.parquet"
                )

        print(f"Completed")


futures_stocks_raw()


def data_concat(symbol_id):
    print(f"Started for {symbol_id}")

    ipath = f"/home/<USER>/repos/data_auditing/raw_futidx_fut/{symbol_id}/expiry_wise_parquets"

    data_list = []

    for parquet in os.listdir(ipath):
        data_list.append(pd.read_parquet(f"{ipath}/{parquet}"))

    data = pd.concat(data_list)

    data = data.sort_index()

    data.to_parquet(
        f"/home/<USER>/repos/data_auditing/raw_futidx_fut/{symbol_id}/combined.parquet"
    )


# data_concat(symbol_id="5008")
