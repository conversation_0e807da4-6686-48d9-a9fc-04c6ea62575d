import sys
import pandas as pd

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import Python<PERSON>perator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.MCX.daily_appends_mcx import DailyAppendsMCX


dailyAppends = DailyAppendsMCX(exchange_type="mcx")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES_MCX = np.load(
    BytesIO(
        minioClient.get_object("commondata", "balte_uploads/ALL_DATES_MCX.npy").data
    ),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2025, month=4, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="MCX_DAILY_DATA",
    schedule_interval="35 23 * * *",
    default_args=args,
    catchup=False,
)


today = pd.Timestamp.now().normalize()
yesterday = today - pd.Timedelta(days=1)


def check_holiday(**kwargs):
    if today not in ALL_DATES_MCX:
        return "HOLIDAY"
    return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)

CHECK_HOLIDAY = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY >> HOLIDAY
CHECK_HOLIDAY >> WORKING_DAY


RAW_MCX_FUT_NEAR = PythonOperator(
    task_id="RAW_MCX_FUT_NEAR",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "mcx_fut_near"},
    dag=dag,
)

MCX_FUT_NEAR_ONEMIN = PythonOperator(
    task_id="MCX_FUT_NEAR_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "mcx_fut_near"},
    dag=dag,
)

MCX_FUT_NEAR_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="MCX_FUT_NEAR_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "mcx_fut_near", "frequency": 1},
    dag=dag,
)

MCX_FUT_NEAR = PythonOperator(
    task_id="MCX_FUT_NEAR",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "mcx_fut_near"},
    dag=dag,
)

MCX_FUT_NEAR_COLUMN_BUCKET = PythonOperator(
    task_id="MCX_FUT_NEAR_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "mcx_fut_near", "frequency": 5},
    dag=dag,
)

RAW_MCX_SPOT = PythonOperator(
    task_id="RAW_MCX_SPOT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "mcx_spot"},
    dag=dag,
)

MCX_SPOT_ONEMIN = PythonOperator(
    task_id="MCX_SPOT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "mcx_spot"},
    dag=dag,
)

MCX_SPOT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="MCX_SPOT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "mcx_spot", "frequency": 1},
    dag=dag,
)

MCX_SPOT = PythonOperator(
    task_id="MCX_SPOT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "mcx_spot"},
    dag=dag,
)

MCX_SPOT_COLUMN_BUCKET = PythonOperator(
    task_id="MCX_SPOT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "mcx_spot", "frequency": 5},
    dag=dag,
)

RAW_OPTCOM = PythonOperator(
    task_id="RAW_OPTCOM",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "optcom"},
    dag=dag,
)

OPTCOM_ONEMIN = PythonOperator(
    task_id="OPTCOM_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "optcom"},
    dag=dag,
)

OPTCOM_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="OPTCOM_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optcom", "frequency": 1},
    dag=dag,
)

OPTCOM = PythonOperator(
    task_id="OPTCOM",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "optcom"},
    dag=dag,
)

OPTCOM_COLUMN_BUCKET = PythonOperator(
    task_id="OPTCOM_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optcom", "frequency": 5},
    dag=dag,
)

RAW_MCX_BHAV = PythonOperator(
    task_id="RAW_MCX_BHAV",
    python_callable=dailyAppends.append_raw_bhav_copy,
    op_kwargs={"symbol": "mcx_bhav"},
    dag=dag,
)

MCX_BHAV = PythonOperator(
    task_id="MCX_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
        "symbol": "mcx_bhav",
    },
    dag=dag,
)

MCX_FUT_BHAV = PythonOperator(
    task_id="MCX_FUT_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
        "symbol": "mcx_fut_bhav",
    },
    dag=dag,
)

MCX_INDEX_BHAV = PythonOperator(
    task_id="MCX_INDEX_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
        "symbol": "mcx_index_bhav",
    },
    dag=dag,
)

MCX_OPT_BHAV = PythonOperator(
    task_id="MCX_OPT_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
        "symbol": "mcx_opt_bhav",
    },
    dag=dag,
)

AFTER_MARKET_GRPC = PythonOperator(
    task_id="AFTER_MARKET_GRPC",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
    },
)

WORKING_DAY >> RAW_MCX_FUT_NEAR
WORKING_DAY >> MCX_FUT_NEAR_ONEMIN
MCX_FUT_NEAR_ONEMIN >> MCX_FUT_NEAR
MCX_FUT_NEAR_ONEMIN >> MCX_FUT_NEAR_ONEMIN_COLUMN_BUCKET
MCX_FUT_NEAR >> MCX_FUT_NEAR_COLUMN_BUCKET

WORKING_DAY >> RAW_MCX_SPOT
WORKING_DAY >> MCX_SPOT_ONEMIN
MCX_SPOT_ONEMIN >> MCX_SPOT
MCX_SPOT_ONEMIN >> MCX_SPOT_ONEMIN_COLUMN_BUCKET
MCX_SPOT >> MCX_SPOT_COLUMN_BUCKET

WORKING_DAY >> RAW_OPTCOM
WORKING_DAY >> OPTCOM_ONEMIN
OPTCOM_ONEMIN >> OPTCOM
OPTCOM_ONEMIN >> OPTCOM_ONEMIN_COLUMN_BUCKET
OPTCOM >> OPTCOM_COLUMN_BUCKET

WORKING_DAY >> RAW_MCX_BHAV
WORKING_DAY >> MCX_BHAV

WORKING_DAY >> MCX_FUT_BHAV

WORKING_DAY >> MCX_INDEX_BHAV

WORKING_DAY >> MCX_OPT_BHAV

WORKING_DAY >> AFTER_MARKET_GRPC
