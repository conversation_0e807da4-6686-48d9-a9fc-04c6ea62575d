"""Operations module for data processing and transformation in the compiler class.

This module provides a set of operations that can be applied to financial data,
including options data, underlying data, and universe data. It includes operations
for adding open interest, put-call ratio, Greeks, filtering data, and more.
"""

from typing import Any, Dict, List, Optional, cast, TypedDict
from main.data.auditor import Auditor
from main.config.config_base import ConfigBase
import pandas as pd
import numpy as np
from main.data.utility import (
    column_bucket_casting,
    get_iv,
    get_delta,
    get_gamma,
    get_library_name,
    get_theta,
    get_vega,
    handle_expiry,
    handle_strike,
)
from main.enums import Operation, StorageType


class OperationArgs(TypedDict, total=False):
    """TypedDict for arguments passed to operations.

    This class defines the possible arguments that can be passed to different
    operations in the Operator class with its expected data type. All fields are optional.
    """

    option_df: pd.DataFrame
    option_oi_df: pd.DataFrame
    underlying_df: pd.DataFrame
    universe_df: pd.DataFrame
    frequency: int
    dtype: str
    id_list: pd.DataFrame
    series_filter_list: List[str]
    symbol_change: pd.DataFrame
    demerger_merger: pd.DataFrame
    expiry_dict: Dict[pd.Timestamp, Dict[str, pd.Timestamp]]
    lotsize: pd.DataFrame
    symbol_map: Dict[str, int]
    corpact_info: pd.DataFrame
    reporate_info: pd.DataFrame
    corpact_dividend_info: pd.DataFrame
    start_date: pd.Timestamp
    end_date: pd.Timestamp
    ID: int
    expiry_categories: List[str]
    filter_rank: int
    value_column: str
    ffill: bool


class Operator(Auditor):
    """Class for performing various operations on financial data.

    This class extends the Auditor class and provides methods for applying
    different operations on financial data, such as adding open interest,
    calculating put-call ratio, computing Greeks, filtering data, and more.
    """

    def __init__(self, config: ConfigBase) -> None:
        """Initialize the Operator with a configuration.

        Args:
            config: Configuration object containing settings for operations.

        Example:
            ```python
            from main.config.config_base import ConfigBase
            config = ConfigBase()
            operator = Operator(config=config)
            ```
        """
        super().__init__(config=config)

    def apply_operation(
        self,
        symbol: str,
        operation_name: Operation,
        universe_name: str,
        **kwargs: Any,
    ) -> pd.DataFrame:
        """Maps the operation and universe to specific operation with required parameters.

        This method routes the operation request to the appropriate private method
        based on the operation_name and provides the necessary arguments from kwargs.

        Args:
            symbol: String representing the symbol name.
            operation_name: Enum value from Operation specifying which operation to perform.
            universe_name: String representing the name of the data universe.
            **kwargs: Variable keyword arguments matching the OperationArgs TypedDict.

        Raises:
            Exception: If an invalid operation is specified or if required arguments
                are missing for a specific operation.

        Returns:
            DataFrame with added or modified columns generated through the specified operation.

        Example:
            ```python
            # Add open interest to option data
            result_df = operator.apply_operation(
                symbol="5001",
                operation_name=Operation.OI,
                universe_name="opt",
                option_df=option_dataframe,
                option_oi_df=option_oi_dataframe
            )
            ```
        """
        args = cast(OperationArgs, kwargs)
        if operation_name == Operation.OI:
            return self.__add_oi(
                option_df=args["option_df"],
                option_oi_df=args["option_oi_df"],
            )
        elif operation_name == Operation.PCR:
            return self.__add_pcr(
                universe=universe_name,
                underlying_df=args["underlying_df"],
                option_oi_df=args["option_oi_df"],
                bhav_data=args["bhav_data"],
                all_dates=args["all_dates"],
            )
        elif operation_name == Operation.GREEKS:
            return self.__add_greeks(
                option_df=args["option_df"],
                underlying_df=args["underlying_df"],
                option_universe=universe_name,
            )
        elif operation_name == Operation.FILTER:
            if "id_list" in args:
                return self.__filter_ID(
                    symbol=symbol,
                    universe=universe_name,
                    data=args["universe_df"],
                    filtering_list=args["id_list"],
                )
            elif "series_filter_list" in args:
                return self.__filter_series(
                    universe=universe_name,
                    data=args["universe_df"],
                    series_filter_list=args["series_filter_list"],
                )
            elif "expiry_dict" in args:
                return self.__filter_by_expiry_rank(
                    data=args["universe_df"],
                    expiry_dict=args["expiry_dict"],
                    expiry_categories=args["expiry_categories"],
                    filter_rank=args["filter_rank"],
                )
            elif "symbol_change" in args:
                return self.__filter_symbol_change(
                    symbol=symbol,
                    universe=universe_name,
                    frequency=args["frequency"],
                    dtype=args["dtype"],
                    filtering_list=args["symbol_change"],
                    start_date=args["start_date"],
                    end_date=args["end_date"],
                )
            elif "demerger_merger" in args:
                return self.__filter_demerger_merger(
                    symbol=symbol,
                    ID=args["ID"],
                    universe=universe_name,
                    data=args["universe_df"],
                    filtering_list=args["demerger_merger"],
                )
            else:
                raise Exception("Invalid arguments passed for FILTER operation!!")
        elif operation_name == Operation.NEXT_CONS_VOLUME:
            return self.__add_next_cons_volume(
                universe=universe_name,
                data=args["option_df"],
                underlying_data=args["underlying_df"],
                expiry_dict=args["expiry_dict"],
            )
        elif operation_name == Operation.CONS_VOLUME:
            return self.__add_cons_volume(
                universe=universe_name,
                data=args["option_df"],
                underlying_data=args["underlying_df"],
            )
        elif operation_name == Operation.MODIFY:
            if "lotsize" in args:
                return self.__round_to_nearest_lotsize(
                    data=args["universe_df"], lotsize=args["lotsize"]
                )
            elif "symbol_map" in args:
                return self.__pivot_by_expiry(
                    data=args["universe_df"],
                    expiry_dict=args["expiry_dict"],
                    value_column=args["value_column"],
                    expiry_categories=args["expiry_categories"],
                    symbol=symbol,
                    symbol_map=args["symbol_map"],
                    universe=universe_name,
                    ffill=args["ffill"],
                )
            elif "corpact_info" in args:
                return self.__apply_corpact(
                    symbol=symbol,
                    universe=universe_name,
                    data=args["universe_df"],
                    corpact_info=args["corpact_info"],
                )
            elif "reporate_info" in args:
                return self.__apply_basis_adjustment(
                    universe=universe_name,
                    data=args["universe_df"],
                    reporate_info=args["reporate_info"],
                )
            elif "corpact_dividend_info" in args:
                return self.__apply_dividend_removal(
                    universe=universe_name,
                    data=args["universe_df"],
                    corpact_dividend_info=args["corpact_dividend_info"],
                    expiry_dict=args["expiry_dict"],
                )
            else:
                raise Exception("Invalid arguments passed for MODIFY operation!!")
        else:
            raise Exception(
                "Invalid operation, should be one of the values in Operation enum!!"
            )

    def __round_to_nearest_lotsize(
        self, data: pd.DataFrame, lotsize: pd.DataFrame
    ) -> pd.DataFrame:
        """Round values in the dataframe to the nearest lot size.

        This method rounds the values in the 'near_month', 'next_month', and 'far_month'
        columns to the nearest lot size based on the provided lot size dataframe.

        Args:
            data: DataFrame containing values to be rounded.
            lotsize: DataFrame containing lot size information indexed by date.

        Returns:
            DataFrame with values rounded to the nearest lot size.
        """
        data.index = cast(pd.DatetimeIndex, data.index)
        data["date"] = pd.to_datetime(data.index.date)
        lotsize = lotsize[lotsize.index.isin(data["date"])]
        symbol_data = data.reset_index().set_index("date")

        for col in ["near_month", "next_month", "far_month"]:
            symbol_data[col] = np.round(symbol_data[col].div(lotsize[col], axis=0))
            symbol_data[col] = np.maximum(symbol_data[col], 1).mul(lotsize[col], axis=0)

        symbol_data = (
            symbol_data.reset_index().set_index("timestamp").drop(columns="date")
        )
        data.drop(columns="date", inplace=True)
        mask = (data == 0) | (symbol_data.isna())
        data.where(mask, symbol_data, inplace=True)
        # Round to nearest integer if lotsize is not present
        data = data.round()
        return data

    def __pivot_by_expiry(
        self,
        universe: str,
        symbol: str,
        data: pd.DataFrame,
        expiry_dict: Dict[pd.Timestamp, Dict[str, pd.Timestamp]],
        value_column: str = "OI",
        expiry_categories: Optional[List[str]] = None,
        symbol_map: Optional[Dict[str, int]] = None,
        ffill: bool = False,
    ) -> pd.DataFrame:
        """Pivot data by expiry dates into separate columns.

        This method pivots data by categorizing it into configurable expiry
        categories (e.g., near_month, next_month, far_month) and pivoting the data
        so each category becomes a separate column.

        Args:
            universe: String representing the name of the data universe.
            symbol: String representing the security symbol.
            data: DataFrame containing the data to be pivoted.
            expiry_dict: Dictionary mapping dates to expiry information.
            value_column: Name of the value column to pivot on (default: "OI").
            expiry_categories: List of expiry categories to use (default: ["near_month", "next_month", "far_month"]).
            symbol_map: Dictionary mapping symbol strings to integer IDs (required if universe is "futidx_fut_oi").
            ffill: Whether to forward fill the category columns (default: False).

        Returns:
            DataFrame with pivoted data where each expiry category is a separate column.
        """
        if expiry_categories is None:
            expiry_categories = ["near_month", "next_month", "far_month"]

        if len(data) == 0:
            return data

        expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")[
            expiry_categories
        ]
        expiry_df.index.name = "date"

        data.index = cast(pd.DatetimeIndex, data.index)
        data["date"] = pd.to_datetime(data.index.date)
        data["timestamp"] = data.index
        if "expiry" not in data.columns:
            data["expiry"] = pd.to_datetime(
                data["ID"].astype(str).str[:8], format="%Y%m%d"
            )
        data = data[data["timestamp"].dt.date <= data["expiry"].dt.date]
        data = data.reset_index(drop=True)
        data = pd.merge(data, expiry_df.reset_index(), how="left", on=["date"])

        data["expiry_rank"] = np.nan
        for i, category in enumerate(expiry_categories, 1):
            data.loc[data["expiry"] == data[category], "expiry_rank"] = i

        data = data[~(data["expiry_rank"].isna())]
        data.expiry_rank = data.expiry_rank.astype("int").astype("str")

        pivot_columns = ["timestamp", "ID", "expiry", value_column, "expiry_rank"]
        data = data[pivot_columns]
        new_df = data.pivot(
            index="timestamp", columns="expiry_rank", values=value_column
        )

        rank_to_category = {
            str(i): category for i, category in enumerate(expiry_categories, 1)
        }
        new_df = new_df.rename(columns=rank_to_category)
        for category in expiry_categories:
            if category not in new_df.columns:
                new_df[category] = np.nan

        if universe == "futidx_fut_oi":
            assert symbol_map is not None
            new_symbol: int = symbol_map[symbol]
        else:
            new_symbol = int(symbol)

        new_df["ID"] = new_symbol

        if ffill:
            for category in expiry_categories:
                new_df[category] = new_df[category].ffill()

        column_order = ["ID"] + expiry_categories
        new_df = new_df[column_order]
        return new_df

    def __filter_series(
        self, universe: str, data: pd.DataFrame, series_filter_list: List[str]
    ) -> pd.DataFrame:
        """Filter the dataframe based on a series filter list.

        This method filters the dataframe to include only rows where the 'series'
        column value is in the provided series_filter_list.

        Args:
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other filter methods.
            data: DataFrame to be filtered.
            series_filter_list: List of strings representing the series values to keep.

        Returns:
            DataFrame containing only rows where the 'series' column value is in the
            provided series_filter_list.
        """
        new_df = data[data.series.isin(series_filter_list)]
        return new_df

    def __add_oi(
        self, option_df: pd.DataFrame, option_oi_df: pd.DataFrame
    ) -> pd.DataFrame:
        """Add open interest data to the option dataframe.

        This method joins the option dataframe with the option open interest dataframe
        and forward-fills any missing open interest values within each ID group.

        Args:
            option_df: DataFrame containing options data.
            option_oi_df: DataFrame containing options open interest data.

        Raises:
            Exception: If the operation fails due to any issues during processing.

        Returns:
            DataFrame with open interest column added to the original option dataframe.
        """
        try:
            option_df = pd.merge(
                option_df, option_oi_df, how="left", on=["timestamp", "ID"]
            )

            oi_column_name = "OI" if "OI" in option_df else "Open_int"
            option_df[oi_column_name] = option_df.groupby("ID")[oi_column_name].ffill()

            option_df = option_df.rename(
                columns={
                    "symbol_x": "symbol",
                    "option_type_x": "option_type",
                    "strike_x": "strike",
                }
            )
        except Exception as exception:
            raise Exception(f"Add OI operation failed due to {repr(exception)}")

        return option_df

    def __add_pcr(
        self,
        universe: str,
        underlying_df: pd.DataFrame,
        option_oi_df: pd.DataFrame,
        bhav_data: pd.DataFrame,
        all_dates: List[pd.Timestamp],
    ) -> pd.DataFrame:
        """Add put-call ratio (PCR) to the underlying dataframe.

        This method calculates the put-call ratio based on the open interest
        of put and call options, and adds it as a column to the underlying dataframe.
        The PCR is calculated as the ratio of put option OI to call option OI.

        Args:
            underlying_df: DataFrame containing underlying asset data.
            option_oi_df: DataFrame containing options open interest data.

        Raises:
            Exception: If the operation fails due to any issues during processing.

        Returns:
            DataFrame with put-call ratio column added to the original underlying dataframe.
        """
        try:
            all_dates_to_index = {}
            index_to_all_dates = {}

            for i, j in enumerate(all_dates):
                all_dates_to_index[j] = i
                index_to_all_dates[i] = j

            bhav_data = bhav_data.rename(
                columns={"balte_id": "ID", "open_interest": "OI", "expiry_date": "expiry", "strike_price": "strike"}
            )
            bhav_data.option_type = bhav_data.option_type.map({"CE": 1, "PE": 0})
            bhav_oi = bhav_data[["ID", "symbol", "expiry", "option_type", "strike", "OI"]]
            bhav_oi["shifted_date"] = bhav_oi.index.map(all_dates_to_index)
            bhav_oi.shifted_date += 1
            bhav_oi.shifted_date = bhav_oi.shifted_date.map(index_to_all_dates)
            bhav_oi.shifted_date += pd.Timedelta(
                hours=int(self._config.MARKET_HOURS_DICT[universe]["open"].split(":")[0]),
                minutes=int(self._config.MARKET_HOURS_DICT[universe]["open"].split(":")[1]),
            )
            bhav_oi = bhav_oi.set_index("shifted_date")
            bhav_oi.index.name = "timestamp"

            option_oi_df = pd.concat([option_oi_df, bhav_oi])
            option_oi_df = option_oi_df.sort_values("ID")
            option_oi_df = option_oi_df.sort_index()
            option_oi_df = option_oi_df.reset_index()

            option_oi_df["OI_delta"] = (
                option_oi_df.groupby([option_oi_df.timestamp.dt.date, "ID"])["OI"]
                .diff()
                .fillna(option_oi_df["OI"])
            )

            option_oi_df = option_oi_df[
                option_oi_df.timestamp.dt.date <= option_oi_df["expiry"].dt.date
            ]

            option_oi_df = pd.DataFrame(
                option_oi_df.groupby(["timestamp", "symbol", "option_type"])[
                    "OI_delta"
                ].sum()
            ).reset_index()

            option_oi_df["OI_delta"] = option_oi_df.groupby(
                [option_oi_df.timestamp.dt.date, "symbol", "option_type"]
            )["OI_delta"].cumsum()

            option_oi_df_call = option_oi_df[option_oi_df.option_type == 1]
            option_oi_df_put = option_oi_df[option_oi_df.option_type == 0]

            option_oi_df = pd.merge(
                option_oi_df_call,
                option_oi_df_put,
                on=["timestamp", "symbol"],
                how="inner",
            )
            option_oi_df["pcr"] = (
                option_oi_df["OI_delta_y"] / option_oi_df["OI_delta_x"]
            )
            option_oi_df = option_oi_df[["timestamp", "symbol", "pcr"]].set_index('timestamp')

            underlying_df = pd.merge(
                underlying_df, option_oi_df, on=["timestamp", "symbol"], how="left"
            )

            underlying_df["pcr"] = underlying_df.groupby([underlying_df.index.date, "expiry"])["pcr"].ffill()

        except Exception as exception:
            raise Exception(f"Add PCR operation failed due to {repr(exception)}")

        return underlying_df

    def __add_greeks(
        self, option_df: pd.DataFrame, underlying_df: pd.DataFrame, option_universe: str, daylight_saving_days: Optional[pd.DataFrame] = None,
    ) -> pd.DataFrame:
        """Add option Greeks (IV, delta, gamma, theta, vega) to the option dataframe.

        This method calculates various option Greeks (implied volatility, delta, gamma,
        theta, vega) for options and adds them as columns to the option dataframe.

        Args:
            option_df: DataFrame containing options data.
            underlying_df: DataFrame containing underlying asset data.
            option_universe: String representing the name of the option universe.

        Raises:
            Exception: If the operation fails due to any issues during processing.

        Returns:
            DataFrame with option Greeks (IV, delta, gamma, theta, vega) added to
            the original option dataframe.
        """
        try:
            if len(option_df) == 0:
                return option_df

            underlying_df_temp = underlying_df.rename(
                columns={"Close": "Close_y"}
            )
            underlying_df_temp = underlying_df_temp.reset_index().set_index(
                ["timestamp", "symbol"]
            )
            option_df = option_df.rename(columns={"Close": "Close_x"})
            option_df = option_df.reset_index().set_index(["timestamp", "symbol"])

            option_df = option_df.join(underlying_df_temp[["Close_y"]])

            expiry_time_dict = {}
            if daylight_saving_days is not None:
                expiry_time_dict = dict.fromkeys(daylight_saving_days.iloc[:, 0], self._config.EXPIRY_TIME_DAYLIGHT_SAVING_DAYS)
            
            option_df["years_to_expiry"] = pd.to_datetime(option_df["expiry"].map(expiry_time_dict).fillna(self._config.EXPIRY_TIME))
            option_df["years_to_expiry"] += option_df["expiry"].dt.normalize()

            option_df["years_to_expiry"] -= option_df.index.get_level_values(0)
            option_df["years_to_expiry"] = option_df[
                "years_to_expiry"
            ].dt.total_seconds() / (31536000)

            interest_rate = self._config.INTEREST_RATE
            option_df["option_type"] = option_df["ID"].apply(
                lambda balte_id: "p"
                if (int(int(balte_id) / (int(1e4))) % int(10)) == 0
                else "c"
            )

            option_df["iv"] = get_iv(
                option_df["Close_x"],
                option_df["Close_y"],
                option_df["strike_price"],
                option_df["years_to_expiry"],
                interest_rate,
                option_df["option_type"],
                self._config.GREEKS_FROM_BLACK,
            )

            option_df["delta"] = get_delta(
                option_df["option_type"],
                option_df["Close_y"],
                option_df["strike_price"],
                option_df["years_to_expiry"],
                interest_rate,
                option_df["iv"],
                self._config.GREEKS_FROM_BLACK,
            )

            option_df["gamma"] = get_gamma(
                option_df["option_type"],
                option_df["Close_y"],
                option_df["strike_price"],
                option_df["years_to_expiry"],
                interest_rate,
                option_df["iv"],
                self._config.GREEKS_FROM_BLACK,
            )

            option_df["theta"] = get_theta(
                option_df["option_type"],
                option_df["Close_y"],
                option_df["strike_price"],
                option_df["years_to_expiry"],
                interest_rate,
                option_df["iv"],
                self._config.GREEKS_FROM_BLACK,
            )

            option_df["vega"] = get_vega(
                option_df["option_type"],
                option_df["Close_y"],
                option_df["strike_price"],
                option_df["years_to_expiry"],
                interest_rate,
                option_df["iv"],
                self._config.GREEKS_FROM_BLACK,
            )

            option_df = option_df.reset_index().set_index("timestamp")
            cols_to_drop = [
                col_d
                for col_d in [
                    "Close_y",
                    "strike_price",
                    "years_to_expiry",
                    "option_type",
                    "sym_ID",
                ]
                if col_d not in self._config.COLUMNS_DICT[option_universe]
            ]
            option_df = option_df.drop(columns=cols_to_drop)
            option_df = option_df.rename(columns={"Close_x": "Close"})
        except Exception as exception:
            raise Exception(f"Add Greeks operation failed due to {repr(exception)}")

        return option_df

    def __add_cons_volume(
        self, universe: str, data: pd.DataFrame, underlying_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Add cumulative volume to the orders data dataframe.

        This adds cumulative volume from trade data (trd) to the orders data (ord)
        and forward-fills any missing values within each date and ID group. Cumulative
        volume represents the total traded volume accumulated till that timestamp.

        Args:
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other methods.
            data: Orders DataFrame (ord) to which cumulative volume will be added.
                Must have timestamp index and ID column.
            underlying_data: Trade DataFrame (trd) containing the cumulative volume data.
                Must have 'Cons_Volume' column with cumulative volume till each timestamp.

        Returns:
            DataFrame with cumulative volume added to the original orders dataframe.
            Missing volume values are forward-filled within each date and ID group.
        """
        ord_df = data.reset_index()
        trd_df = underlying_data.reset_index()

        merged_df = pd.merge(
            ord_df,
            trd_df[["timestamp", "ID", "Cons_Volume"]],
            on=["timestamp", "ID"],
            how="left",
        )

        merged_df["Cons_Volume"] = (
            merged_df.assign(date=merged_df["timestamp"].dt.date)
            .groupby(["date", "ID"])["Cons_Volume"]
            .ffill()
        )

        merged_df = merged_df.set_index("timestamp")
        return merged_df

    ## TODO: try to use __filter_near_contract method
    def __add_next_cons_volume(
        self,
        universe: str,
        data: pd.DataFrame,
        underlying_data: pd.DataFrame,
        expiry_dict: Dict[pd.Timestamp, Dict[str, pd.Timestamp]],
    ) -> pd.DataFrame:
        """Add next contract cumulative volume to the orders data dataframe.

        This method adds the cumulative volume of the next month's contract to the orders data
        based on expiry dates. It categorizes contracts into near_month and next_month,
        then adds the next month's cumulative volume to near month contracts.

        Args:
            universe: String representing the name of the data universe.
            data: Orders DataFrame (ord) to which next contract cumulative volume will be added.
            underlying_data: Trade DataFrame (trd) containing the cumulative volume data.
                Must have 'Cons_Volume' column with cumulative volume till each timestamp.
            expiry_dict: Dictionary mapping timestamps to expiry information for categorization.

        Returns:
            DataFrame with next contract cumulative volume added to the original orders dataframe.
            Near month contracts get 'Next_Cons_Volume' from next month's cumulative volume.
        """
        data.index = cast(pd.DatetimeIndex, data.index)
        underlying_data.index = cast(pd.DatetimeIndex, underlying_data.index)
        data["expiry"] = pd.to_datetime(data["ID"].astype(str).str[:8], format="%Y%m%d")
        underlying_data["expiry"] = pd.to_datetime(
            underlying_data["ID"].astype(str).str[:8], format="%Y%m%d"
        )
        data["date"] = data.index.normalize()
        underlying_data["date"] = underlying_data.index.normalize()

        data = data.reset_index()
        underlying_data = underlying_data.reset_index()

        expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")[
            ["near_month", "next_month"]
        ]
        expiry_df.index.name = "date"
        data = pd.merge(data, expiry_df.reset_index(), how="left", on=["date"])
        underlying_data = pd.merge(
            underlying_data, expiry_df.reset_index(), how="left", on=["date"]
        )

        data["expiry_rank"] = np.nan
        data.loc[data["expiry"] == data["near_month"], "expiry_rank"] = 1
        data.loc[data["expiry"] == data["next_month"], "expiry_rank"] = 2
        underlying_data["expiry_rank"] = np.nan
        underlying_data.loc[
            underlying_data["expiry"] == underlying_data["near_month"], "expiry_rank"
        ] = 1
        underlying_data.loc[
            underlying_data["expiry"] == underlying_data["next_month"], "expiry_rank"
        ] = 2

        underlying_data["expiry_rank"] = underlying_data["expiry_rank"] - 1

        data_expnna = data[~data.expiry_rank.isna()]
        data_expna = data[data.expiry_rank.isna()]

        data_expnna = data_expnna.merge(
            underlying_data[["timestamp", "Cons_Volume", "expiry_rank"]],
            on=["timestamp", "expiry_rank"],
            how="left",
            suffixes=("", "_Next"),
        )

        data_expnna = data_expnna.rename(
            columns={"Cons_Volume_Next": "Next_Cons_Volume"}
        )
        data_expna["Next_Cons_Volume"] = np.nan

        data = pd.concat([data_expnna, data_expna])

        data = data.drop(
            columns=["expiry", "expiry_rank", "date", "near_month", "next_month"]
        )

        data = data.set_index("timestamp").sort_index()
        return data

    def resample_data_1_min_to_5_min(
        self, universe: str, data: pd.DataFrame
    ) -> pd.DataFrame:
        """Resample 1-minute data to 5-minute data.

        This method resamples 1-minute frequency data to 5-minute frequency data
        using the aggregation functions defined in the configuration. It also ensures
        that data types remain consistent after resampling.

        Args:
            universe: String representing the name of the data universe.
            data: DataFrame with 1-minute data, having a timestamp as an index.

        Raises:
            Exception: If extra columns are found that are not present in SAMPLING_AGG_DICT.

        Returns:
            DataFrame with 5-minute data, having a timestamp as an index.

        Example:
            ```python
            resampled_df = operator.resample_data_1_min_to_5_min(
                universe="opt",
                data=one_minute_dataframe
            )
            ```
        """
        extra_columns: List[str] = []
        sampling_agg_dict: Dict[str, str] = {}
        for column in data.columns:
            if column in self._config.SAMPLING_AGG_DICT:
                sampling_agg_dict[column] = self._config.SAMPLING_AGG_DICT[column]
            elif column != "ID":
                extra_columns.append(column)

        if len(extra_columns):
            raise Exception(
                f"resample_data_1_min_to_5_min error: extra columns: {extra_columns} in data"
            )

        resample_data = pd.DataFrame(
            (
                data.groupby("ID")
                .resample(rule="5T", label="right", closed="right")  # type: ignore
                .agg(sampling_agg_dict)
                .dropna(how="all")
                .reset_index()
                .set_index("timestamp")
                .sort_index()
            )
        )

        resample_data = resample_data[resample_data["Close"] > 0]

        data_types_dict = self._config.DATA_TYPES_DICT.copy()
        update_dict = self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT.get(universe)
        if update_dict:
            data_types_dict.update(update_dict)

        # Ensuring data types remain consistent after resampling, as missing entries can introduce NaNs, which change the dtype, for e.g. int to float
        for column in resample_data.columns:
            resample_data[column] = resample_data[column].astype(
                dtype=cast(Any, data_types_dict[column])
            )

        return resample_data

    def create_column_bucket(
        self, universe: str, data: pd.DataFrame
    ) -> Dict[str, pd.DataFrame]:
        """Create column buckets for each column of the given data.

        This method creates a dictionary of column buckets where each key is a column name
        and each value is a DataFrame containing that column's data pivoted by time.

        Args:
            universe: String representing the name of the data universe.
            data: Combined DataFrame of all symbols of the universe having timestamp as an index.

        Raises:
            Exception: If pivoting of the DataFrame encounters any issues.

        Returns:
            Dictionary where keys are column names and values are DataFrames with
            ['date', 'ID'] as an index. Each DataFrame represents a column bucket.

        Example:
            ```python
            column_buckets = operator.create_column_bucket(
                universe="optidx_opt",
                data=option_dataframe
            )
            ```
        """
        columns = data.columns
        data["time"] = cast(pd.Timestamp, data.index).strftime("%H:%M")
        data["date"] = cast(pd.DatetimeIndex, data.index).date

        try:
            data = data.set_index(["date", "ID"]).pivot(columns="time")
        except Exception as exception:
            raise Exception(f"Creation of column bucket fails due to {repr(exception)}")

        column_to_data = {}

        data_types_dict = self._config.DATA_TYPES_DICT.copy()
        if universe in self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT:
            for column in self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT[
                universe
            ].keys():
                data_types_dict[
                    column
                ] = self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT[universe][
                    column
                ]

        for column in columns:
            if column not in self._config.IGNORE_COLUMNS_COLUMN_BUCKET_LIST:
                data_column = column_bucket_casting(
                    universe=universe,
                    column_name=column,
                    data=data[column].reset_index(),
                    data_types_dict=data_types_dict,  # type: ignore
                )
                data_column = data_column.set_index(["date", "ID"])
                column_to_data[column] = data_column
        return column_to_data

    def __filter_ID(
        self,
        universe: str,
        symbol: str,
        data: pd.DataFrame,
        filtering_list: pd.DataFrame,
    ) -> pd.DataFrame:
        """Filter data based on ID from the filtering list.

        This method filters the data to include only rows where the ID matches
        the symbol in the filtering list.

        Args:
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other filter methods.
            symbol: String representing the security symbol.
            data: DataFrame to be filtered.
            filtering_list: DataFrame containing the list of IDs to filter by.

        Returns:
            DataFrame containing only rows where the ID matches the symbol in the filtering list.
        """
        filtering_list_index_name = filtering_list.index.name
        data.index = cast(pd.DatetimeIndex, data.index)
        filtering_list.index = cast(pd.DatetimeIndex, filtering_list.index)
        filtering_list = filtering_list[filtering_list.ID == int(symbol)]

        if len(filtering_list) == 0:
            return pd.DataFrame(columns=data.columns)

        if "date" not in data.columns and "date" not in data.index.names:
            data["date"] = data.index.normalize()

        filtering_list = filtering_list[["ID"]].reset_index()

        if "date" not in filtering_list.columns:
            filtering_list["date"] = filtering_list[
                filtering_list_index_name
            ].dt.normalize()

        filtered_df = pd.merge(
            data.reset_index(), filtering_list, how="inner", on=["date"]
        )
        filtered_df = filtered_df.drop(columns=["date", "ID_y"]).rename(
            columns={"ID_x": "ID"}
        )
        filtered_df = filtered_df.set_index("timestamp")

        return filtered_df

    def __filter_by_expiry_rank(
        self,
        data: pd.DataFrame,
        expiry_dict: Dict[pd.Timestamp, Dict[str, pd.Timestamp]],
        expiry_categories: Optional[List[str]] = None,
        filter_rank: int = 1,
    ) -> pd.DataFrame:
        """Filter data to include only contracts of a specific expiry rank.

        This method filters the data to include only rows where the expiry date
        matches the specified expiry rank (e.g., 1=near_month, 2=next_month, 3=far_month).

        Args:
            data: DataFrame to be filtered.
            expiry_dict: Dictionary mapping dates to expiry information.
            expiry_categories: List of expiry categories to use (default: ["near_month", "next_month", "far_month"]).
            filter_rank: Which rank to filter by (1=first category, 2=second category, etc.).

        Returns:
            DataFrame containing only rows where the expiry date matches the
            specified expiry rank.
        """
        if expiry_categories is None:
            expiry_categories = ["near_month", "next_month", "far_month"]

        if len(data) == 0:
            return data

        # Add expiry column if it doesn't exist
        if "expiry" not in data.columns:
            data["expiry"] = pd.to_datetime(
                data["ID"].astype(str).str[:8], format="%Y%m%d"
            )

        # Create expiry dataframe with specified categories
        expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")[
            expiry_categories
        ]
        expiry_df.index.name = "date"
        data.index = cast(pd.DatetimeIndex, data.index)
        data["date"] = pd.to_datetime(data.index.date)
        data["timestamp"] = data.index

        data = data.reset_index(drop=True)
        data = pd.merge(data, expiry_df.reset_index(), how="left", on=["date"])

        data["expiry_rank"] = np.nan
        for i, category in enumerate(expiry_categories, 1):
            data.loc[data["expiry"] == data[category], "expiry_rank"] = i

        data = data[data["expiry_rank"] == filter_rank]
        columns_to_drop = ["expiry", "expiry_rank", "date"] + expiry_categories
        data = data.drop(
            columns=[col for col in columns_to_drop if col in data.columns]
        )
        data = data.set_index("timestamp")
        return data

    def __filter_symbol_change(
        self,
        symbol: str,
        universe: str,
        frequency: int,
        dtype: str,
        filtering_list: pd.DataFrame,
        start_date: pd.Timestamp,
        end_date: pd.Timestamp,
    ) -> pd.DataFrame:
        """Filter data based on symbol changes over time.

        This method handles symbol changes over time by reading data for all
        relevant symbols and concatenating them based on their valid time periods.

        Args:
            symbol: String representing the current security symbol.
            universe: String representing the name of the data universe.
            frequency: Integer representing data frequency in minutes.
            dtype: String representing data type.
            filtering_list: DataFrame containing symbol change information.
            start_date: Timestamp representing start date for data.
            end_date: Timestamp representing end date for data.

        Returns:
            DataFrame containing data for the symbol, accounting for symbol changes over time.
        """
        symbol_list_df = filtering_list[filtering_list["symbol"] == symbol]
        if len(symbol_list_df) > 0 and symbol != symbol_list_df["current"].iloc[0]:
            return pd.DataFrame()
        symbol_change = filtering_list[filtering_list.current == symbol]
        symbol_list = symbol_change.symbol.tolist()

        if len(symbol_list) == 0:
            symbol_list.append(symbol)

        symbol_to_duration = {}
        if len(symbol_list) > 1:
            for ind in range(len(symbol_change)):
                sdate = (
                    pd.Timestamp(2007, 1, 1)
                    if ind == 0
                    else symbol_change.index[ind - 1]
                )
                edate = (
                    self._config.DATE_TODAY
                    if ind == len(symbol_change) - 1
                    else symbol_change.index[ind]
                )
                symbol_to_duration[symbol_change.iloc[ind]["symbol"]] = (sdate, edate)

        df_list = []
        for sym in symbol_list:
            try:
                df = self.read(
                    storage_type=StorageType.DB,
                    file_location=get_library_name(
                        exchange_name=self._config.EXCHANGE,
                        frequency=frequency,
                        universe_name=universe,
                        dtype=dtype,
                        storage=StorageType.DB,
                    ),
                    file_name=sym,
                    start_date=start_date,
                    end_date=end_date,
                )
                if sym in symbol_to_duration:
                    df = df[
                        (df.index >= symbol_to_duration[sym][0])
                        & (df.index < symbol_to_duration[sym][1])
                    ]
            except Exception as e:
                print(f"Got execption while reading {sym}")
                df = pd.DataFrame()

            df_list.append(df)

        raw_df: pd.DataFrame = pd.concat(df_list)
        raw_df["Symbol"] = symbol

        if "symbol" in raw_df.columns:
            raw_df["symbol"] = symbol

        return raw_df

    def __filter_demerger_merger(
        self,
        symbol: str,
        ID: int,
        universe: str,
        data: pd.DataFrame,
        filtering_list: pd.DataFrame,
    ) -> pd.DataFrame:
        """Filter data based on demerger/merger information.

        This method handles demerger/merger events by updating the ID column
        in the data based on the ex-date of the demerger/merger events.

        Args:
            symbol: String representing the security symbol.
            ID: Integer representing the current security ID.
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other filter methods.
            data: DataFrame to be filtered.
            filtering_list: DataFrame containing demerger/merger information.

        Returns:
            DataFrame with updated ID column based on demerger/merger events.
        """
        if len(data) == 0:
            return data

        different_IDs = filtering_list[filtering_list["Symbol"] == symbol]
        unique_IDs = [ID]

        different_IDs = different_IDs.reset_index()

        for ind in range(len(different_IDs) - 1, -1, -1):
            exdate = different_IDs.iloc[ind].exdate
            data.loc[data.index < exdate, "ID"] = different_IDs.iloc[ind].ID
            unique_IDs.append(different_IDs.iloc[ind].ID)

        return data

    def __apply_corpact(
        self, symbol: str, universe: str, data: pd.DataFrame, corpact_info: pd.DataFrame
    ) -> pd.DataFrame:
        """Apply corporate action adjustments to data.

        This method applies corporate action adjustments to the data by multiplying
        relevant columns by adjustment factors based on ex-dates.

        Args:
            symbol: String representing the security symbol.
                This parameter is kept for consistency with other methods.
            universe: String representing the name of the data universe.
            data: DataFrame to which corporate action adjustments will be applied.
            corpact_info: DataFrame containing corporate action information.

        Returns:
            DataFrame with corporate action adjustments applied.
        """
        corpact_num = len(corpact_info)
        data["adj_factor"] = 1

        for index in range(corpact_num - 1, -1, -1):
            adj_factor = corpact_info.iloc[index]["adj_factor"]
            exdate = corpact_info.index[index]
            data.loc[data.index < exdate, "adj_factor"] *= adj_factor

        for col in data.columns:
            if col == "adj_factor":
                continue
            if col in self._config.CORPACT_ADJUSTMENT_ACTIONS:
                data[col] = self._config.CORPACT_ADJUSTMENT_ACTIONS[col](
                    cast("pd.Series[float]", data[col]), data.adj_factor
                )

        if "adj_factor" not in self._config.COLUMNS_DICT[universe]:
            data = data.drop(columns=["adj_factor"])
        ## add operations

        return data

    def __apply_basis_adjustment(
        self, universe: str, data: pd.DataFrame, reporate_info: pd.DataFrame
    ) -> pd.DataFrame:
        """Apply basis adjustments to futures data based on repo rates and time to expiry.

        This method adjusts futures prices to account for the cost of carry by applying
        the formula: adjusted_price = original_price * (1 - repo_rate * time_to_expiry_years).

        Args:
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other methods.
            data: DataFrame to which basis adjustments will be applied.
            reporate_info: DataFrame containing repo rate information.

        Returns:
            DataFrame with basis adjustments applied to configured price columns.
            Temporary columns (expiry, date, repo_rate) are removed from output.

        Note:
            Empty DataFrames are returned unchanged. Repo rates are forward-filled for missing dates.
        """
        if len(data) == 0:
            return data
        data.index = cast(pd.DatetimeIndex, data.index)
        data["expiry"] = pd.to_datetime(data["ID"].astype(str).str[:8], format="%Y%m%d")
        data["date"] = data.index.normalize()

        reporate_info = reporate_info.reindex(
            sorted(set(data.date.unique().tolist() + reporate_info.index.to_list()))
        )
        if np.isnan(reporate_info["repo_rate"].iloc[0]):
            reporate_info["repo_rate"].iloc[0] = reporate_info[
                ~reporate_info["repo_rate"].isna()
            ].iloc[0]
        reporate_info["repo_rate"] = reporate_info["repo_rate"].ffill()

        data = data.reset_index()
        data = pd.merge(data, reporate_info, how="left", on=["date"])
        data = data.set_index("timestamp")
        # time to expiry in years
        data["repo_rate"] *= (data.expiry - data.date).dt.days / 365

        for column in self._config.BASIS_ADJUSTMENT_COLUMNS:
            if column in data:
                data[column] = data[column] * (1 - data["repo_rate"])

        data = data.drop(columns=["expiry", "date", "repo_rate"])
        return data

    def __apply_dividend_removal(
        self,
        universe: str,
        data: pd.DataFrame,
        corpact_dividend_info: pd.DataFrame,
        expiry_dict: Dict[pd.Timestamp, Dict[str, pd.Timestamp]],
    ) -> pd.DataFrame:
        """Remove dividend impact from price data to create ex-dividend adjusted prices.

        Adjusts historical price data by removing dividend effects to maintain price continuity.
        For futures data, corporate actions with adjustment factors > 0.98 (less than 2% impact)
        are removed from announcement date until ex-date, but only for contracts expiring before
        the ex-date.

        The adjustment is needed because:
        - Stock prices drop by dividend amount on ex-dividend date
        - Historical analysis requires continuous price series
        - Removes artificial price gaps caused by dividend payments

        Args:
            universe: String representing the name of the data universe.
                This parameter is kept for consistency with other methods.
            data: DataFrame containing price data to be dividend-adjusted.
            corpact_dividend_info: DataFrame with dividend adjustment factors in column "adj_factor".
                Each factor represents (1 + dividend_yield) for the respective period.
            expiry_dict: Dictionary mapping dates to expiry information for futures contracts.

        Returns:
            DataFrame with dividend-adjusted price columns. Price levels are reduced
            proportionally to remove dividend impact from historical data.

        Raises:
            ValueError: If one or more dividend factors are 0, which would cause
                division by zero and invalid price adjustments.

        Note:
            Missing dividend factors are filled with 1.0 (no adjustment).
        """
        if len(data) == 0 or len(corpact_dividend_info) == 0:
            return data
        data.index = cast(pd.DatetimeIndex, data.index)
        initial_columns = data.columns

        corpact_dividend_info["adj_factor"] = corpact_dividend_info[
            "adj_factor"
        ].fillna(1)

        # Validate that no dividend factors are zero
        if (corpact_dividend_info["adj_factor"] == 0).any():
            raise ValueError(
                "One or more dividend factors are 0, cannot apply dividend removal"
            )

        if "date" not in data.columns:
            data["date"] = data.index.normalize()
        if "adj_factor" not in data.columns:
            data["adj_factor"] = 1

        data["expiry"] = pd.to_datetime(data["ID"].astype(str).str[:8], format="%Y%m%d")
        expiry_df = pd.DataFrame.from_dict(expiry_dict, orient="index")[
            ["near_month", "next_month"]
        ]
        expiry_df.index.name = "date"
        data = pd.merge(data, expiry_df.reset_index(), how="left", on=["date"])

        for ann_date, row in corpact_dividend_info.iterrows():
            data.loc[
                (data.date >= ann_date)
                & (data.expiry == data.near_month)
                & (data.date < row["exdate"])
                & (data.expiry >= row["exdate"]),
                "adj_factor",
            ] *= row["adj_factor"]
        for column in self._config.BASIS_ADJUSTMENT_COLUMNS:
            if column in data.columns:
                data[column] /= data["adj_factor"]

        return data[initial_columns]
