from io import BytesIO
import numpy as np
import pandas as pd
from daily_appends.daily_appends_base import DailyAppendsBase

from main.data.utility import get_library_name, previous_date
from main.enums import StorageType


class DailyAppendsMCX(DailyAppendsBase):
    def __preprocess_bhav_data(self, bhav_universe, df, prev_date):
        if bhav_universe == "mcx_bhav":
            df.columns = [x.lower() for x in df.columns]
            df["date"] = pd.to_datetime(df["date"], format="%d %b %Y")
            df = df.set_index("date")
            df["eod_price"] = df["close"]

            return df
        elif bhav_universe in ["mcx_fut_bhav", "mcx_opt_bhav", "mcx_index_bhav"]:
            instrument_map = {
                "mcx_fut_bhav": "FUTCOM",
                "mcx_opt_bhav": "OPTFUT",
                "mcx_index_bhav": "FUTIDX",
            }
            df.columns = [x.lower() for x in df.columns]

            df["expiry date"] = pd.to_datetime(df["expiry date"], format="%d%b%Y")
            df["date"] = pd.to_datetime(df["date"], format="%d %b %Y")

            df["balte_id"] = df["symbol"].map(self._symbol_to_balte_id)
            df.loc[np.isnan(df.balte_id), "balte_id"] = 0

            ## Filter by segment
            df = df[df["instrument name"] == instrument_map[bhav_universe]]

            df["expiry_rank"] = df.groupby("symbol")["expiry date"].rank(method="dense")

            if bhav_universe in ["mcx_opt_bhav"]:
                strike = (df["strike price"] * 100).astype(int).astype(str)
                expiry = (df["expiry date"].dt.strftime("%Y%m%d")).str[2:]
                option = df["option type"].map({"CE": "1", "PE": "0"})
                ID = df["balte_id"].astype(int).astype(str)
                df["balte_id"] = strike + expiry + option + ID

            df = df.set_index("date")

            ## Adding eod_price column for backtesting support
            df["eod_price"] = df["close"]
            return df
        else:
            raise Exception(f"Bhavcopy handling not added for {bhav_universe}")

    def append_raw_bhav_copy(self, symbol, date=None):
        if date is None:
            date = self._config.DATE_TODAY

        raw_symbol = f"raw_{symbol}"

        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=raw_symbol,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_copy = pd.read_csv(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata",
                    file_name=self._config.AFTER_MARKET_DICT_MINIO[symbol],
                ).data
            )
        )

        bhav_copy.columns = [x.lower() for x in bhav_copy.columns]
        bhav_copy["date"] = pd.to_datetime(bhav_copy["date"], format="%d %b %Y")
        bhav_copy = bhav_copy.set_index("date")

        bhav_copy = self._handle_data(universe=raw_symbol, data=bhav_copy)

        symbol_list = self._tanki[library_name].list_symbols()

        failed_syms = []

        for symbol_bhav, symbol_bhav_df in bhav_copy.groupby("symbol"):
            try:
                self._push_data_to_db(
                    library_name=library_name,
                    symbol=symbol_bhav,
                    data=symbol_bhav_df,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as e:
                failed_syms.append(f"{symbol_bhav}: {e}")

        if len(failed_syms):
            raise Exception(
                f"DailyAppendsError: Following raw bhav symbols are failed to append:\n{failed_syms}"
            )

    def append_daily_data_1440(self, universe, timing, symbol=None):
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=universe,
            dtype="trd",
            storage=StorageType.DB,
        )

        if symbol is not None:
            bhav_universe = symbol

            prev_date = previous_date(self._all_dates)

            bhav_copy = pd.read_csv(
                BytesIO(
                    self._get_from_minio(
                        file_location="commondata",
                        file_name=self._config.AFTER_MARKET_DICT_MINIO[bhav_universe],
                    ).data
                )
            )
            try:
                processed_copy = self.__preprocess_bhav_data(
                    bhav_universe,
                    bhav_copy,
                    prev_date,
                )

                processed_copy = self._handle_data(bhav_universe, processed_copy)

                self._push_data_to_db(
                    library_name=library_name,
                    symbol=symbol,
                    data=processed_copy,
                    date=prev_date,
                )
            except Exception as exception:
                raise Exception(
                    f"DailyAppendsError: {bhav_universe} failed to append due to {exception}\n"
                )
        else:
            grpc_symbols_failed_to_append = []
            symbol_list = self._tanki[library_name].list_symbols()
            for symbol in self._config.AFTER_MARKET_DICT_GRPC:
                symbol_data = self._toti_obj.get_fresh_1440(
                    symbol, self._config.DATE_TODAY
                )
                if len(symbol_data) == 0:
                    continue
                try:
                    symbol_data = symbol_data.set_index("date")
                    symbol_data = self._handle_data(universe=symbol, data=symbol_data)
                    self._push_data_to_db(
                        library_name=library_name,
                        symbol=symbol,
                        data=symbol_data,
                        symbol_list=symbol_list,
                    )
                except Exception as exception:
                    grpc_symbols_failed_to_append.append(f"{symbol}: {exception}")

            if grpc_symbols_failed_to_append:
                raise Exception(
                    f"DailyAppendsError: Following symbols are failed to append:\n{grpc_symbols_failed_to_append}\n"
                )
