from io import BytesIO
from minio import Minio
import numpy as np
import pandas as pd
import os
from multiprocessing import Pool
import pickle
import re
import zstandard as zstd


def get_symbol_to_balte_id():
    with open("/home/<USER>/repos/data_auditing/symbol_to_balte_id", "rb") as f:
        return pickle.load(f)


base_path = "/mnt/companydata/MarketData/eq/tick/root_ord"
starting_balte_id = 1
ending_balte_id = 5000
cash_type = "FF"
symbol_to_balte_id = get_symbol_to_balte_id()
errors_on_the_fly_shared_dict = {}

MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)

ALL_DATES = np.load(
    BytesIO(
        minio_client_143.get_object("commondata", "balte_uploads/ALL_DATES.npy").data
    ),
    allow_pickle=True,
)

start_date = pd.Timestamp("2022-04-19")
end_date = pd.Timestamp("2022-06-05")
valid_dates_list = [
    date.strftime("%d%m%Y") for date in ALL_DATES if start_date <= date <= end_date
]
valid_dates_list += [pd.Timestamp(2021, 4, 29).strftime("%d%m%Y")]
print("\nGot date list...\n")


# /mnt/companydata/MarketData/eq/tick/root_ord/29042021/NIFTY/29042021/CE/NIFTY29-Apr-2021CE1520000.ord.zst
def get_sym_paths(date):
    print(f"Started for {date}")
    path1 = f"{base_path}/{date}"
    try:
        for sym in os.listdir(path1):
            if (sym not in symbol_to_balte_id) or (
                symbol_to_balte_id[sym] >= starting_balte_id
                and symbol_to_balte_id[sym] <= ending_balte_id
            ):
                path2 = f"{path1}/{sym}"
                for exp in os.listdir(path2):
                    if len(exp) == 8 and exp.isdigit():
                        path3 = f"{path2}/{exp}"
                        for typ in os.listdir(path3):
                            if typ == "FF":
                                continue
                            path4 = f"{path3}/{typ}"
                            for contract in os.listdir(path4):
                                try:
                                    path5 = f"{path4}/{contract}"
                                    path_split = path5.split("/")[1:]
                                    date_str = path_split[6]

                                    pattern_zst = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.ord\.zst"
                                    pattern = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.ord"
                                    match_zst = re.match(pattern_zst, path_split[-1])
                                    match = re.match(pattern, path_split[-1])

                                    if match_zst:
                                        expiry = match_zst.group(2)
                                        strike = match_zst.group(4)
                                    elif match:
                                        expiry = match.group(2)
                                        strike = match.group(4)
                                    else:
                                        continue

                                    if match:
                                        tick_data_csv = pd.read_csv(path5, header=None)
                                    else:
                                        with open(path5, "rb") as f_in:
                                            dctx = zstd.ZstdDecompressor()
                                            decompressed_data = dctx.stream_reader(f_in)
                                            decompressed_buffer = BytesIO(
                                                decompressed_data.read()
                                            )

                                            tick_data_csv = pd.read_csv(
                                                decompressed_buffer, header=None
                                            )

                                    # timestamp, best-bid, best-ask
                                    tick_data_csv = tick_data_csv[[0, 2, 17]]
                                    tick_data_csv["timestamp"] = pd.to_datetime(
                                        tick_data_csv[0],
                                        unit="s",
                                        origin=pd.Timestamp(
                                            day=int(date_str[0:2]),
                                            month=int(date_str[2:4]),
                                            year=int(date_str[4:]),
                                        ),
                                    )
                                    tick_data_csv = tick_data_csv.set_index("timestamp")
                                    tick_data_csv[1] = (
                                        tick_data_csv[2] + tick_data_csv[17]
                                    ) / 200
                                    tick_data_csv = tick_data_csv.drop(columns=[2, 17])

                                    tick_data_csv = tick_data_csv.rename(
                                        columns={
                                            0: "time_in_seconds_count",
                                            1: "ltp",
                                        }
                                    )

                                    tick_data_csv = tick_data_csv.resample(
                                        "1T", closed="right", label="right"
                                    ).agg(
                                        {
                                            "ltp": ["first", "max", "min", "last"],
                                        }
                                    )[
                                        "ltp"
                                    ]

                                    tick_data_csv = tick_data_csv.dropna()

                                    tick_data_csv.columns = [
                                        "Open",
                                        "High",
                                        "Low",
                                        "Close",
                                    ]
                                    tick_data_csv["ID"] = sym
                                    tick_data_csv["expiry"] = expiry
                                    tick_data_csv["strike"] = int(strike) / 100
                                    tick_data_csv["option_type"] = (
                                        1 if typ == "CE" else 0
                                    )

                                    if not os.path.exists(
                                        f"/home/<USER>/repos/data_auditing/raw_opt_ord_tick/{sym}/"
                                    ):
                                        os.makedirs(
                                            f"/home/<USER>/repos/data_auditing/raw_opt_ord_tick/{sym}/",
                                            exist_ok=True,
                                        )

                                    tick_data_csv.to_parquet(
                                        f"/home/<USER>/repos/data_auditing/raw_opt_ord_tick/{sym}/{date_str}_{expiry}_{typ}_{strike}.parquet"
                                    )
                                except Exception as e:
                                    with open(
                                        "error_getting_raw_opt_ord_tick", "a"
                                    ) as f:
                                        f.write(
                                            f"{sym}_{date_str}_{expiry}_{typ}_{strike} : {e}\n"
                                        )
    except Exception as e:
        if not os.path.exists(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_ord/"
        ):
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/failed_raw_opt_ord/",
                exist_ok=True,
            )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_ord/{date}.txt", "w"
        ) as f:
            f.write(f"Failed for {date} due to: {e}\n")
        return

    os.makedirs(
        f"/home/<USER>/repos/data_auditing/success_raw_opt_ord/{date}/",
        exist_ok=True,
    )


# get_sym_paths(valid_dates_list[-1])

# [get_sym_paths(date) for date in valid_dates_list]

# print(f"Starting compiling tick data...")
# with Pool(25) as p:
#     p.map(get_sym_paths, valid_dates_list)
# print(f"Completed compiling tick data :)")


# print(f"Completed combining {count} tick data files at {pd.Timestamp.now()}\n")
