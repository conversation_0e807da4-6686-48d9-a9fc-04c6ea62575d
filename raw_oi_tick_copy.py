import datetime
import calendar
from functools import partial
from io import BytesIO
import shutil
from minio import Minio
import numpy as np
import pandas as pd
import os
import time
from multiprocessing import Pool, Manager
import pickle
import re
import zstandard as zstd
from arcticdb import Arctic


def get_symbol_to_balte_id():
    # get from balte and save it
    with open("/home/<USER>/repos/data_auditing/symbol_to_balte_id", "rb") as f:
        return pickle.load(f)


base_path = "/mnt/companydata/MarketData/eq/tick/root_oi"
starting_balte_id = 1
ending_balte_id = 5000
cash_type = "FF"
symbol_to_balte_id = get_symbol_to_balte_id()
errors_on_the_fly_shared_dict = {}

MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)

ALL_DATES = np.load(
    BytesIO(
        minio_client_143.get_object("commondata", "balte_uploads/ALL_DATES.npy").data
    ),
    allow_pickle=True,
)

start_date = pd.Timestamp("2025-3-1")
end_date = pd.Timestamp("2025-06-04")
valid_dates_list = [
    date.strftime("%d%m%Y") for date in ALL_DATES if start_date <= date <= end_date
]

print("\nGot date list...\n")

sym_date_paths = {}
with open("/home/<USER>/repos/data_auditing/sym_to_date_oi_paths.pkl", "rb") as f:
    sym_date_paths = pickle.load(f)


# /mnt/companydata/MarketData/eq/tick/root_ord/29042021/NIFTY/29042021/CE/NIFTY29-Apr-2021CE1520000.ord.zst
def get_sym_paths(date):
    if os.path.exists(f"/home/<USER>/repos/data_auditing/success_raw_opt_oi_tick/{date}/"):
        return
    print(f"Started for {date}")
    path1 = f"{base_path}/{date}"
    try:
        for sym in os.listdir(path1):
            if (sym not in symbol_to_balte_id) or (
                symbol_to_balte_id[sym] >= starting_balte_id
                and symbol_to_balte_id[sym] <= ending_balte_id
            ):
                path2 = f"{path1}/{sym}"
                df_list = []
                for exp in os.listdir(path2):
                    if len(exp) == 8 and exp.isdigit():
                        path3 = f"{path2}/{exp}"
                        for typ in os.listdir(path3):
                            if typ == "FF":
                                continue
                            path4 = f"{path3}/{typ}"
                            for contract in os.listdir(path4):
                                try:
                                    path5 = f"{path4}/{contract}"
                                    path_split = path5.split("/")[1:]
                                    date_str = path_split[6]

                                    pattern_zst = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.oi\.zst"
                                    pattern = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.oi"
                                    match_zst = re.match(pattern_zst, path_split[-1])
                                    match = re.match(pattern, path_split[-1])

                                    if match_zst:
                                        expiry = match_zst.group(2)
                                        strike = match_zst.group(4)
                                    elif match:
                                        expiry = match.group(2)
                                        strike = match.group(4)
                                    else:
                                        continue

                                    if match:
                                        tick_data_csv = pd.read_csv(path5, header=None)
                                    else:
                                        with open(path5, "rb") as f_in:
                                            dctx = zstd.ZstdDecompressor()
                                            decompressed_data = dctx.stream_reader(f_in)
                                            decompressed_buffer = BytesIO(
                                                decompressed_data.read()
                                            )

                                            tick_data_csv = pd.read_csv(
                                                decompressed_buffer, header=None
                                            )

                                    tick_data_csv["timestamp"] = pd.to_datetime(
                                        tick_data_csv[0],
                                        unit="s",
                                        origin=pd.Timestamp(
                                            day=int(date_str[0:2]),
                                            month=int(date_str[2:4]),
                                            year=int(date_str[4:]),
                                        ),
                                    )
                                    tick_data_csv = tick_data_csv.set_index("timestamp")

                                    tick_data_csv = tick_data_csv.drop(columns=[0])
                                    tick_data_csv = tick_data_csv.rename(
                                        columns={
                                            1: "OI",
                                        }
                                    )

                                    tick_data_csv = tick_data_csv.resample(
                                        "1T", closed="right", label="right"
                                    ).agg(
                                        {
                                            "OI": "last",
                                        }
                                    )

                                    tick_data_csv = tick_data_csv.dropna()

                                    tick_data_csv.columns = ["OI"]
                                    tick_data_csv["ID"] = sym
                                    tick_data_csv["expiry"] = expiry
                                    tick_data_csv["strike"] = int(strike) / 100
                                    tick_data_csv["option_type"] = (
                                        1 if typ == "CE" else 0
                                    )

                                    df_list.append(tick_data_csv)
                                except Exception as e:
                                    with open(
                                        "error_getting_raw_opt_oi_tick", "a"
                                    ) as f:
                                        f.write(
                                            f"{sym}_{date_str}_{expiry}_{typ}_{strike} : {e}\n"
                                        )

                if len(df_list) == 0:
                    continue
                tick_data = pd.concat(df_list)
                tick_data = tick_data.sort_index()

                if not os.path.exists(
                    f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/"
                ):
                    os.makedirs(
                        f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/",
                        exist_ok=True,
                    )
                tick_data.to_parquet(
                    f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/{date}.parquet"
                )

    except Exception as e:
        if not os.path.exists(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/"
        ):
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/",
                exist_ok=True,
            )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/{date}.txt",
            "w",
        ) as f:
            f.write(f"Failed for {date} due to: {e}\n")
        return

    os.makedirs(
        f"/home/<USER>/repos/data_auditing/success_raw_opt_oi_tick/{date}/",
        exist_ok=True,
    )



def get_data(path):
    try:
        path_split = path.split("/")[1:]
        date_str = path_split[6]

        pattern_zst = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.oi\.zst"
        pattern = r"([^\s]+)(\d{2}-[A-Za-z]+-\d{4})(CE|PE)(\d+)\.oi"
        match_zst = re.match(pattern_zst, path_split[-1])
        match = re.match(pattern, path_split[-1])

        if match_zst:
            sym = match_zst.group(1)
            expiry = match_zst.group(2)
            strike = match_zst.group(4)
        elif match:
            sym = match.group(1)
            expiry = match.group(2)
            strike = match.group(4)
        else:
            return

        if match:
            tick_data_csv = pd.read_csv(path, header=None)
        else:
            with open(path, "rb") as f_in:
                dctx = zstd.ZstdDecompressor()
                decompressed_data = dctx.stream_reader(f_in)
                decompressed_buffer = BytesIO(decompressed_data.read())

                tick_data_csv = pd.read_csv(decompressed_buffer, header=None)

        tick_data_csv["timestamp"] = pd.to_datetime(
            tick_data_csv[0],
            unit="s",
            origin=pd.Timestamp(
                day=int(date_str[0:2]),
                month=int(date_str[2:4]),
                year=int(date_str[4:]),
            ),
        )
        tick_data_csv = tick_data_csv.set_index("timestamp")

        tick_data_csv = tick_data_csv.drop(columns=[0])
        tick_data_csv = tick_data_csv.rename(
            columns={
                1: "OI",
            }
        )

        tick_data_csv = tick_data_csv.resample(
            "1T", closed="right", label="right"
        ).agg(
            {
                "OI": "last",
            }
        )

        tick_data_csv = tick_data_csv.dropna()

        tick_data_csv.columns = ["OI"]
        tick_data_csv["ID"] = sym
        tick_data_csv["expiry"] = expiry
        tick_data_csv["strike"] = int(strike) / 100
        tick_data_csv["option_type"] = 1 if match.group(3) == "CE" else 0

        tick_data_csv.to_parquet(
            f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/{path.replace('/','_')}.parquet"
        )
    except Exception as e:
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/{sym}/",
            exist_ok=True,
        )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/{sym}/{path.replace('/','_')}.txt",
            "w",
        ) as f:
            f.write(f"{e}\n")


def get_tick_data_for_sym(sym):
    print(f"Started for {sym}")

    try:
        storea = Arctic(
            "s3://*************:9000:arctic-db?access=super&secret=doopersecret"
        )
        liboi = storea["nse/1_min/raw_optstk_oi/trd"]

        start_date = pd.Timestamp(liboi.get_description(sym).date_range[0].date())

        dfoi = liboi.read(sym, date_range=(pd.Timestamp(2021, 12, 10), None)).data

        alld = [
            d.date() for d in ALL_DATES if start_date <= d <= pd.Timestamp(2025, 6, 4)
        ]

        missing_dates = set(alld) - set(dfoi.index.date)

        del dfoi

        paths = []
        for date in missing_dates:
            if sym not in sym_date_paths:
                continue
            if date.strftime("%d%m%Y") not in sym_date_paths[sym]:
                continue
            paths.extend(sym_date_paths[sym][date.strftime("%d%m%Y")])

        if len(paths) == 0:
            return

        os.makedirs(
            f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/", exist_ok=True
        )

        # get_data(paths[0])
        # [get_data(path) for path in paths]

        with Pool(15) as p:
            p.map(get_data, paths)

        dfn_list = []

        for data in os.listdir(
            f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}"
        ):
            dfn_list.append(
                pd.read_parquet(
                    f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/{data}"
                )
            )

        dfn = pd.concat(dfn_list).sort_index()
        dfn.to_parquet(
            f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/combined.parquet"
        )
        for data in os.listdir(
            f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}"
        ):
            if "combined" in data:
                continue
            os.remove(
                f"/home/<USER>/repos/data_auditing/raw_opt_oi_tick/{sym}/{data}"
            )

        print(f"Completed for {sym}")

    except Exception as e:
        os.makedirs(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/",
            exist_ok=True,
        )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_raw_opt_oi_tick/{sym}.txt",
            "w",
        ) as f:
            f.write(f"Failed for {sym} due to: {e}\n")
        return


# get_tick_data_for_sym(sym="BOSCHLTD")


# storea = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# liboi = storea["nse/1_min/raw_optstk_oi/trd"]
# syms = liboi.list_symbols()
# [get_tick_data_for_sym(sym) for sym in syms[: len(syms) // 2]]


# get_sym_paths(valid_dates_list[2])

# [get_sym_paths(date) for date in valid_dates_list]

print(f"Starting compiling tick data...")
with Pool(8) as p:
    p.map(get_sym_paths, valid_dates_list)
print(f"Completed compiling tick data :)")


# print(f"Completed combining {count} tick data files at {pd.Timestamp.now()}\n")
