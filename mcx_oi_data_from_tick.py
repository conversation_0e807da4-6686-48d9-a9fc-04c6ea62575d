from io import Bytes<PERSON>
from minio import Minio
import numpy as np
import pandas as pd
import os
from multiprocessing import Pool
import pickle
import re
import zstandard as zstd



MINIO_END_POINT_219 = "*************:11009"
MINIO_ACCESS_KEY = "minioreader"
MINIO_SECRET_KEY = "reasersecret"

minio_client_143 = Minio(
    MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False
)

def get_symbol_to_balte_id():
    sid = minio_client_143.get_object("commondata", "balte_uploads/mapping_dict").data
    return pickle.loads(sid)


base_path = "/mnt/companydata/MarketData/com/tick/root_oi"
dtype="oi"
starting_balte_id = 1
ending_balte_id = 5000
cash_type = "FF"
symbol_to_balte_id = get_symbol_to_balte_id()
errors_on_the_fly_shared_dict = {}



ALL_DATES = np.load(
    BytesIO(
        minio_client_143.get_object("commondata", "balte_uploads/ALL_DATES_MCX.npy").data
    ),
    allow_pickle=True,
)

start_date = pd.Timestamp("2024-10-10")
end_date = pd.Timestamp.today().normalize()-pd.Timedelta(days=1)
valid_dates_list = [
    date.strftime("%d%m%Y") for date in ALL_DATES if start_date <= date <= end_date
]
print("\nGot date list...\n")


#/mnt/companydata/MarketData/com/tick/root_trd/10102024/GOLDM/22102024/CE/GOLDM22-Oct-2024CE755000000.trd
def get_sym_paths(date):
    if os.path.exists(f"/home/<USER>/repos/data_auditing/success_mcx_oi_data_from_tick/{date}/"):
        return
    print(f"Started for {date}")
    path1 = f"{base_path}/{date}"
    try:
        for sym in ["GOLDM", "SILVERM"]:
            if (sym not in symbol_to_balte_id) or (
                symbol_to_balte_id[sym] >= starting_balte_id
                and symbol_to_balte_id[sym] <= ending_balte_id
            ):
                df_list_ff=[]
                df_list=[]
                path2 = f"{path1}/{sym}"
                for exp in os.listdir(path2):
                    if len(exp) == 8 and exp.isdigit():
                        path3 = f"{path2}/{exp}"
                        for typ in os.listdir(path3):
                            path4 = f"{path3}/{typ}"
                            for contract in os.listdir(path4):
                                path5 = f"{path4}/{contract}"
                                path_split = path5.split("/")[1:]
                                date_str = path_split[6]

                                pattern_zst = rf"([^\s]+)(\d{{2}}-[A-Za-z]+-\d{{4}})(CE|PE)(\d+)\.{dtype}\.zst"
                                pattern = rf"([^\s]+)(\d{{2}}-[A-Za-z]+-\d{{4}})(CE|PE)(\d+)\.{dtype}"
                                
                                pattern_zst_ff = rf"([^\s]+)(\d{{2}}-[A-Za-z]+-\d{{4}})\.{dtype}\.zst"
                                pattern_ff = rf"([^\s]+)(\d{{2}}-[A-Za-z]+-\d{{4}})\.{dtype}"
                                
                                match_zst = re.match(pattern_zst, path_split[-1])
                                match = re.match(pattern, path_split[-1])
                                
                                match_zst_ff = re.match(pattern_zst_ff, path_split[-1])
                                match_ff = re.match(pattern_ff, path_split[-1])

                                if match_zst:
                                    expiry = match_zst.group(2)
                                    strike = int(match_zst.group(4))/100
                                elif match:
                                    expiry = match.group(2)
                                    strike = int(match.group(4))/100
                                elif match_zst_ff:
                                    expiry = match_zst_ff.group(2)
                                    strike = 0
                                elif match_ff:
                                    expiry = match_ff.group(2)
                                    strike = 0
                                else:
                                    continue

                                if match or match_ff:
                                    tick_data_csv = pd.read_csv(path5, header=None)
                                else:
                                    with open(path5, "rb") as f_in:
                                        dctx = zstd.ZstdDecompressor()
                                        decompressed_data = dctx.stream_reader(f_in)
                                        decompressed_buffer = BytesIO(
                                            decompressed_data.read()
                                        )

                                        tick_data_csv = pd.read_csv(
                                            decompressed_buffer, header=None
                                        )

                                # timestamp and OI
                                tick_data_csv["timestamp"] = pd.to_datetime(
                                    tick_data_csv[0],
                                    unit="s",
                                    origin=pd.Timestamp(
                                        day=int(date_str[0:2]),
                                        month=int(date_str[2:4]),
                                        year=int(date_str[4:]),
                                    ),
                                )
                                tick_data_csv = tick_data_csv.set_index("timestamp")
                                

                                tick_data_csv = tick_data_csv.rename(
                                    columns={
                                        1: "OI",
                                    }
                                )

                                tick_data_csv = tick_data_csv.resample(
                                    "1T", closed="right", label="right"
                                ).agg(
                                    {
                                        "OI": "last",
                                    }
                                )

                                tick_data_csv = tick_data_csv.dropna()

                                tick_data_csv["ID"] = sym
                                tick_data_csv["expiry"] = expiry
                                if (strike) > 0:
                                    tick_data_csv["strike"] = int(strike) / 100
                                    tick_data_csv["option_type"] = (
                                        1 if typ == "CE" else 0
                                    )  
                                    
                                if typ == "FF":
                                    df_list_ff.append(tick_data_csv)
                                else:
                                    df_list.append(tick_data_csv)
                
                if len(df_list):
                    df = pd.concat(df_list)
                    df=df.sort_index()
                    if not os.path.exists(
                        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt/"
                    ):
                        os.makedirs(
                            f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt/"
                        )
                    df.to_parquet(f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt/{date}.parquet")
                if len(df_list_ff):
                    df = pd.concat(df_list_ff)
                    df=df.sort_index()
                    if not os.path.exists(
                        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut/"
                    ):
                        os.makedirs(
                            f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut/"
                        )
                    df.to_parquet(f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut/{date}.parquet")
                
    except Exception as e:
        if not os.path.exists(
            f"/home/<USER>/repos/data_auditing/failed_mcx_oi_data_from_tick/"
        ):
            os.makedirs(
                f"/home/<USER>/repos/data_auditing/failed_mcx_oi_data_from_tick/",
                exist_ok=True,
            )
        with open(
            f"/home/<USER>/repos/data_auditing/failed_mcx_oi_data_from_tick/{date}.txt", "w"
        ) as f:
            f.write(f"Failed for {date} due to: {e}\n")
        return

    os.makedirs(
        f"/home/<USER>/repos/data_auditing/success_mcx_oi_data_from_tick/{date}/",
        exist_ok=True,
    )


# get_sym_paths(valid_dates_list[-2])

# [get_sym_paths(date) for date in valid_dates_list]

# print(f"Starting compiling tick data...")
# with Pool(8) as p:
#     p.map(get_sym_paths, valid_dates_list)
# print(f"Completed compiling tick data :)")


# print(f"Completed combining {count} tick data files at {pd.Timestamp.now()}\n")


from arcticdb import Arctic
from minio import Minio
from main.tanki import Tanki



def combine_oi_opt(sym):
    print(F"Started for {sym}")
    df_list = []
    for d in os.listdir(
        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt/"
    ):
        df_list.append(
            pd.read_parquet(
                f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt/{d}"
            )
        )

    df = pd.concat(df_list)
    df = df.sort_index()
    df.expiry = pd.to_datetime(df.expiry, format="%d-%b-%Y")
    df.option_type = df.option_type.astype("int16")

    df["symbol"] = df["ID"]
    df["ID"] = (
        (df.strike * 10 * int(1e13)).astype("uint64")
        + df.expiry.dt.strftime("%Y%m%d").astype("uint64") * int(1e5)
        + df.option_type.astype("uint64") * int(1e4)
        + {"SILVERM": 7252, "GOLDM": 7251}[sym]
    )
    df = df[
        [
            "ID",
            "symbol",
            "expiry",
            "option_type",
            "strike",
            "OI",
        ]
    ]

    df.to_parquet(
        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/opt_final.parquet"
    )
    
    print(f"Completed for {sym}")


def combine_oi_fut(sym):
    print(F"Started for {sym}")
    df_list = []
    for d in os.listdir(
        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut/"
    ):
        df_list.append(
            pd.read_parquet(
                f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut/{d}"
            )
        )

    df = pd.concat(df_list)
    df = df.sort_index()
    df.expiry = pd.to_datetime(df.expiry, format="%d-%b-%Y")

    df["symbol"] = df["ID"]
    df["ID"] = (
        + df.expiry.dt.strftime("%Y%m%d").astype("uint64") * int(1e5)
        + 2 * int(1e4)
        + {"SILVERM": 7252, "GOLDM": 7251}[sym]
    )
    df = df[
        [
            "ID",
            "symbol",
            "expiry",
            "OI",
        ]
    ]

    df.to_parquet(
        f"/home/<USER>/repos/data_auditing/mcx_oi_data_from_tick/{sym}/fut_final.parquet"
    )
    
    print(f"Completed for {sym}")


def create_opt_bhav_data(sym):
    print(F"Started for {sym}")
    
    storek = Arctic(
        "s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret"
    )
    libo=storek['mcx_old_v2/1440_min/after_market/trd']
    libn=storek['mcx/1440_min/after_market/trd']
    
    MINIO_END_POINT_219 = "*************:11009"
    MINIO_ACCESS_KEY = "minioreader"
    MINIO_SECRET_KEY = "reasersecret"

    minio_client_143 = Minio(MINIO_END_POINT_219, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False)
    md=pickle.loads(minio_client_143.get_object(
                    "commondata",
                   "balte_uploads/mapping_dict",
                ).data)
    md["GOLDM"]=7251
    md["SILVERM"]=7252

    
    dfn=libn.read(sym).data
    
    dfo=libo.read(sym, date_range=(None, dfn.index.min() - pd.Timedelta(days=1))).data
    
    dfo.balte_id=(dfo.strike_price.astype('uint64')*int(1e14)).astype('uint64')+dfo.expiry_date.dt.strftime('%Y%m%d').astype('uint64')*int(1e5)+dfo.option_type.map({'CE': 1, 'PE': 0}).astype('uint64')*int(1e4)+dfo.symbol.map(md).fillna(0).astype('uint64')
    dfo.open_intereset=dfo.open_interest.astype('float64')
    
    df=pd.concat([dfo,dfn])
    df.to_parquet(f"/home/<USER>/repos/data_auditing/{sym}backup.parquet")
        
    libn.delete(sym)
    
    tanki = Tanki(exchange_type="mcx", write=True)
    tanki.login(username="mantraraj", password="mantraraj")
    
    tanki["mcx/1440_min/after_market/trd"].write_metadata(sym, df)
    tanki["mcx/1440_min/after_market/trd"].write(sym, df)
    print(f"Completed for {sym}")


create_opt_bhav_data("mcx_opt_bhav")

# combine_oi_opt("GOLDM")
# combine_oi_opt("SILVERM")

# combine_oi_fut("GOLDM")
# combine_oi_fut("SILVERM")
